{"app": {"name": "VDO Tools", "description": "Fast and easy video and audio conversion tools for all your media needs", "tagline": "Easy media conversion in seconds", "hero": {"title": {"part1": "Convert Any Media", "part2": "In Seconds"}, "description": "Fast, free, and secure file conversion tools for all your media needs. No downloads required. Easy to use. Lightning fast.", "cta": "Explore Tools"}, "features": {"easy": "Easy to use", "fast": "Lightning fast", "secure": "100% secure", "free": "Completely free"}, "animation": {"original": "Original", "converting": "Converting", "converted": "Converted"}}, "nav": {"home": "Home", "tools": "Tools", "about": "About", "features": "Features", "mainFeatures": "Main Features", "advancedFeatures": "Advanced Features", "upcomingFeatures": "Upcoming Features", "feature1": "Feature 1", "feature2": "Feature 2", "feature3": "Feature 3", "feature4": "Feature 4", "feature5": "Feature 5", "feature6": "Feature 6"}, "tools": {"categories": {"video-to-audio": "Video to Audio", "video-format-converters": "Video Format Converters", "audio-format-converters": "Audio Format Converters", "recording-tools": "Recording Tools"}, "universal-format-converter": {"title": "Universal Format Converter", "description": "Auto-detect input format and convert to any format", "instructions": "Upload a video file, we'll detect the format automatically. Then select your desired output format and convert."}, "common": {"upload": "Upload File", "convert": "Convert", "download": "Download", "processing": "Processing...", "dropzone": "Drag and drop your file here, or click to select", "rate": "Rate this tool", "thanks": "Thanks for your rating!", "instructions": "Instructions", "uploadFirst": "Upload a file to get started", "conversionSuccess": "Conversion completed successfully!", "conversionFailed": "Conversion failed. Please try again.", "ffmpegLoading": "Preparing your device for our wizardry...", "changeFile": "Change File"}, "mp4-to-mp3": {"title": "MP4 to MP3 Converter", "description": "Convert MP4 videos to MP3 audio files", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your MP3 audio file."}, "video-to-audio": {"title": "Video to Audio Converter", "description": "Extract audio from any video format", "selectFile": "Select Video File", "instructions": "Upload any video file, click convert, and download the extracted audio."}, "mp4-to-webm": {"title": "MP4 to WebM Converter", "description": "Convert MP4 videos to WebM format", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your WebM video."}, "webm-to-mp4": {"title": "WebM to MP4 Converter", "description": "Convert WebM videos to MP4 format", "selectFile": "Select WebM File", "instructions": "Upload a WebM video file, click convert, and download your MP4 video."}, "mp3-to-wav": {"title": "MP3 to WAV Converter", "description": "Convert MP3 audio to WAV format", "selectFile": "Select MP3 File", "instructions": "Upload an MP3 audio file, click convert, and download your WAV audio."}, "wav-to-mp3": {"title": "WAV to MP3 Converter", "description": "Convert WAV audio to MP3 format", "selectFile": "Select WAV File", "instructions": "Upload a WAV audio file, click convert, and download your MP3 audio."}, "mov-to-mp4": {"title": "MOV to MP4 Converter", "description": "Convert MOV videos to MP4 format", "selectFile": "Select MOV File", "instructions": "Upload a MOV video file, click convert, and download your MP4 video."}, "avi-to-mp4": {"title": "AVI to MP4 Converter", "description": "Convert AVI videos to MP4 format", "selectFile": "Select AVI File", "instructions": "Upload an AVI video file, click convert, and download your MP4 video."}, "mp4-to-gif": {"title": "MP4 to GIF Converter", "description": "Convert MP4 videos to optimized animated GIF images", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your animated GIF. The converter optimizes for file size while maintaining good quality."}, "gif-to-mp4": {"title": "GIF to MP4 Converter", "description": "Convert animated GIF images to MP4 videos", "selectFile": "Select GIF File", "instructions": "Upload an animated GIF file, click convert, and download your MP4 video. Converting to MP4 can significantly reduce file size while preserving animation quality."}, "screen-recorder": {"title": "Screen Recorder", "description": "Record your screen and save as video", "selectFile": "Start Recording", "instructions": "Click the Start Recording button, select the screen or window you want to record, and start capturing. When finished, click Stop and download your recording as WebM or MP4.", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "pauseRecording": "Pause Recording", "resumeRecording": "Resume Recording", "downloadWebM": "Download as WebM", "downloadMP4": "Download as MP4", "convertToMP4": "Convert to MP4", "selectScreen": "Select what to record", "entireScreen": "Entire Screen", "currentWindow": "Current Window", "currentTab": "Current Tab", "withAudio": "Include Audio", "withoutAudio": "Without Audio", "recordingTime": "Recording Time", "preparing": "Preparing recording...", "processing": "Processing recording...", "recordingReady": "Recording is ready!", "recordingFailed": "Recording failed. Please try again.", "conversionFailed": "MP4 conversion failed. You can still download as WebM.", "noPermission": "Permission to record screen was denied. Please allow access and try again.", "browserNotSupported": "Your browser doesn't support screen recording. Please try Chrome, Edge, or Firefox."}, "video-cutter": {"title": "Video Trimmer", "description": "Trim and cut video clips with precision using our easy-to-use video trimmer tool", "selectFile": "Select Video File", "instructions": "Upload a video file, select the range you want to keep using the timeline, and download your trimmed video."}, "video-cropper": {"title": "Video Cropper", "description": "Crop and resize videos online with precision. Remove unwanted areas and adjust aspect ratios for social media, presentations, or any purpose", "selectFile": "Select Video File", "instructions": "Upload a video file, select the crop area using the visual editor, choose your aspect ratio, and download your cropped video."}, "video-trimmer": {"title": "Video Trimmer", "description": "Trim video clips with precision using our advanced timeline editor. Cut unwanted sections and keep only the parts you need", "selectFile": "Select Video File", "instructions": "Upload a video file, use the timeline to select start and end points, and download your trimmed video clip."}}, "seo": {"title": "{toolName} | {appName}", "description": "{toolDescription} - Free online tool, no installation required."}, "footer": {"copyright": "© {year} {appName}. All rights reserved.", "home": "Home", "mp4ToMp3": "MP4 to MP3", "videoToAudio": "Video to Audio", "languages": "Languages", "about": "About Us", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service", "dmca": "DMCA Policy"}, "error": {"notFound": {"title": "404 - Page Not Found", "heading": "Page Not Found", "description": "The page you are looking for doesn't exist or has been moved.", "goHome": "Go Home"}}, "ui": {"toggleTheme": "Toggle theme", "toggleMenu": "Toggle menu", "language": "Language", "search": "Search"}, "sidebar": {"relatedTools": "Related Tools", "searchTools": "Search tools...", "noToolsFound": "No tools found", "allTools": "All Tools"}, "search": {"placeholder": "Search for tools...", "noResults": "No tools found", "popularTools": "Popular Tools", "clear": "Clear search"}, "categories": {"videoToAudioTools": "Video to Audio Tools", "videoTools": "Video Tools", "audioTools": "Audio Tools", "ourServices": "Our Services"}, "formatGroups": {"videoFormats": "Video Formats", "audioFormats": "Audio Formats"}, "tools_list": {"universalFormatConverter": "Universal Format Converter", "mp4ToMp3": "MP4 to MP3", "videoToAudio": "Video to Audio", "mp4ToWebm": "MP4 to WebM", "webmToMp4": "WebM to MP4", "movToMp4": "MOV to MP4", "aviToMp4": "AVI to MP4", "mp4ToGif": "MP4 to GIF", "gifToMp4": "GIF to MP4", "mp3ToWav": "MP3 to WAV", "wavToMp3": "WAV to MP3", "videoConverter": "Video Converter", "videoCompressor": "Video Compressor", "videoEditor": "Video Editor", "videoCutter": "Video Cutter", "videoCropper": "Video Cropper", "videoTrimmer": "Video Trimmer", "audioConverter": "Audio Converter", "audioExtractor": "Audio Extractor", "screenRecorder": "Screen Recorder"}, "dynamicConverter": {"title": "{inputFormat} to {outputFormat} Converter", "description": "Convert {inputFormat} files to {outputFormat} format", "singleFormatTitle": "{format} Converter", "singleFormatDescription": "Convert {format} files to other formats", "selectFile": "Select File", "inputFormatLabel": "Input Format", "outputFormatLabel": "Output Format", "selectInputFormat": "Select Input Format", "selectOutputFormat": "Select Output Format", "settings": "Conversion Settings", "advancedSettings": "Advanced Settings", "showAdvancedSettings": "Show Advanced Settings", "hideAdvancedSettings": "Hide Advanced Settings", "resolution": "Resolution", "quality": "Quality", "fps": "Frame Rate", "audioBitrate": "Audio Bitrate", "original": "Original", "customSettings": "Custom Settings", "applySettings": "Apply Settings", "resetSettings": "Reset to Defaults", "videoSettings": "Video Settings", "audioSettings": "Audio Settings", "keepOriginalSettings": "Keep Original Settings", "showSettings": "Show Settings", "hideSettings": "<PERSON><PERSON> Settings", "expectedFormat": "Expected format: {format}", "customResolution": "Custom Resolution", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "aspectRatio": "Aspect Ratio", "scalingMethod": "Scaling Method", "keyframeInterval": "Keyframe Interval", "videoCodec": "Video Codec", "audioCodec": "Audio Codec", "audioChannels": "Audio Channels", "audioSampleRate": "Sample Rate", "audioVolume": "Volume", "useCustomResolution": "Use Custom Resolution", "advancedVideoSettings": "Advanced Video Settings", "advancedAudioSettings": "Advanced Audio Settings"}, "pages": {"about": {"title": "About Us", "description": "Learn more about VDO Tools, our mission, and the services we provide for video and audio conversion."}, "contact": {"title": "Contact Us", "description": "Get in touch with the VDO Tools team for questions, feedback, or support with our video and audio conversion tools."}, "privacy": {"title": "Privacy Policy", "description": "VDO Tools privacy policy explains how we handle your data when you use our video and audio conversion tools."}, "terms": {"title": "Terms of Service", "description": "Terms and conditions for using VDO Tools video and audio conversion services."}, "dmca": {"title": "DMCA Policy", "description": "VDO Tools DMCA policy and copyright infringement reporting procedures."}}, "converters": {"universalFormatConverter": {"title": "Universal Format Converter", "description": "Auto-detect input format and convert to any format", "selectFile": "Select Video File", "instructions": "Upload a video file, we'll detect the format automatically. Then select your desired output format and convert.", "detectedFormat": "Detected Format", "selectOutputFormat": "Select Output Format", "outputFormatLabel": "Output Format"}, "mp4ToMp3": {"title": "MP4 to MP3 Converter", "description": "Convert MP4 videos to MP3 audio files", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your MP3 audio file."}, "videoToAudio": {"title": "Video to Audio Converter", "description": "Extract audio from any video format", "selectFile": "Select Video File", "instructions": "Upload any video file, click convert, and download the extracted audio."}, "mp4ToWebm": {"title": "MP4 to WebM Converter", "description": "Convert MP4 videos to WebM format", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your WebM video."}, "webmToMp4": {"title": "WebM to MP4 Converter", "description": "Convert WebM videos to MP4 format", "selectFile": "Select WebM File", "instructions": "Upload a WebM video file, click convert, and download your MP4 video."}, "mp3ToWav": {"title": "MP3 to WAV Converter", "description": "Convert MP3 audio to WAV format", "selectFile": "Select MP3 File", "instructions": "Upload an MP3 audio file, click convert, and download your WAV audio."}, "wavToMp3": {"title": "WAV to MP3 Converter", "description": "Convert WAV audio to MP3 format", "selectFile": "Select WAV File", "instructions": "Upload a WAV audio file, click convert, and download your MP3 audio."}, "movToMp4": {"title": "MOV to MP4 Converter", "description": "Convert MOV videos to MP4 format", "selectFile": "Select MOV File", "instructions": "Upload a MOV video file, click convert, and download your MP4 video."}, "aviToMp4": {"title": "AVI to MP4 Converter", "description": "Convert AVI videos to MP4 format", "selectFile": "Select AVI File", "instructions": "Upload an AVI video file, click convert, and download your MP4 video."}, "mp4ToGif": {"title": "MP4 to GIF Converter", "description": "Convert MP4 videos to animated GIF images", "selectFile": "Select MP4 File", "instructions": "Upload an MP4 video file, click convert, and download your animated GIF. Note: GIFs are optimized for size and quality with reduced resolution and frame rate."}, "gifToMp4": {"title": "GIF to MP4 Converter", "description": "Convert animated GIF images to MP4 videos", "selectFile": "Select GIF File", "instructions": "Upload an animated GIF file, click convert, and download your MP4 video. Converting to MP4 can significantly reduce file size while preserving animation quality."}}}